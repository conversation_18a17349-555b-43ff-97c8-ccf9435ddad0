{"name": "rotary-club-tunis", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:standard": "next dev", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@payloadcms/db-mongodb": "^3.53.0", "@payloadcms/next": "^3.53.0", "@payloadcms/payload-cloud": "^3.53.0", "@payloadcms/plugin-cloud-storage": "^3.53.0", "@payloadcms/plugin-seo": "^3.53.0", "@payloadcms/richtext-lexical": "^3.53.0", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.541.0", "next": "15.5.0", "payload": "^3.53.0", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "jest": "^30.0.5", "tailwindcss": "^4", "ts-jest": "^29.4.1", "typescript": "^5"}}