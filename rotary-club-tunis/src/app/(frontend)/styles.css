@import "tailwindcss";
/* Tailwind CSS v4 Theme Configuration for Rotary Tunis Doyen Club */
/* Adheres to Rotary International's official Brand Guidelines (2025) */

/* === CSS CUSTOM PROPERTIES FOR JAVASCRIPT ACCESS === */
/* These variables are accessible via getComputedStyle for StyleDebug component */
:root {
  /* === ROTARY INTERNATIONAL OFFICIAL BRAND COLORS (EXACT HEX) === */
  --color-rotary-blue: #17458f; /* PMS 286C */
  --color-rotary-gold: #f7a81b; /* PMS 130C - OFFICIAL HEX */
  --color-rotary-azure: #0067c8; /* PMS 2175C */
  --color-rotary-sky-blue: #00a2e0; /* PMS 2202C */
  --color-rotary-dark-blue: #003399; /* PMS 661C */

  /* === COULEURS SPÉCIALISÉES ROTARY (LOGOS OFFICIELS) === */
  --color-rotary-cranberry: #d41367; /* PMS 214C - Logo Rotaract */
  --color-rotary-cardinal: #e02927; /* PMS 485C - End Polio Now */
  --color-rotary-turquoise: #00adbb; /* PMS 7466C */
  --color-rotary-orange: #ff7600; /* PMS 2018C */
  --color-rotary-violet: #901f93; /* PMS 2070C */
  --color-rotary-lawn-green: #009739; /* PMS 355C */

  /* === COULEURS NEUTRES OFFICIELLES === */
  --color-rotary-pastel-blue: #b9d9eb; /* PMS 290C */
  --color-rotary-moss-green: #a7aca2; /* PMS 7537C */
  --color-rotary-lavender: #c6bcd0; /* PMS 665C */
  --color-rotary-taupe: #d9c89e; /* PMS 7501C */
  --color-rotary-mouse-gray: #9ba4b4; /* PMS 2162C */
  --color-rotary-slate: #657f99; /* PMS 2165C */
  --color-rotary-anthracite: #54565a; /* Cool Gray 11C */
  --color-rotary-tin: #898a8d; /* Cool Gray 8C */
  --color-rotary-light-gray: #b1b1b1; /* Cool Gray 5C */
  --color-rotary-silver: #d0cfcd; /* Cool Gray 2C */

  /* === ACCESSIBILITY-ENHANCED DERIVATIVE COLORS === */
  --color-rotary-blue-dark: #0f3670;
  --color-rotary-gold-dark: #d68f00;
  --color-rotary-azure-dark: #0055a4;
  --color-rotary-sky-blue-dark: #0088c0;
  --color-rotary-blue-light: #2d5ba0;
  --color-rotary-gold-light: #f9b84a;
  --color-rotary-dark-blue-light: #1a4b9c;

  /* === TYPOGRAPHY SYSTEM (ROTARY INTERNATIONAL OFFICIAL - FREE FONTS ONLY) === */
  /* Define font variables at :root level for proper CSS variable access */
  --font-open-sans: "Open Sans", Arial, system-ui, -apple-system, sans-serif;
  --font-family-primary: var(--font-open-sans);
  --font-family-secondary: Georgia, "Times New Roman", serif;
  --font-family-sans: var(--font-family-primary);
  --font-family-arabic: "Traditional Arabic", Amiri, "Scheherazade New", sans-serif;

  /* === FONT PROPERTIES === */
  --font-weight-regular: 400;
  --font-weight-semibold: 600;
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --radius: 0.625rem;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@theme {
  /* === ROTARY INTERNATIONAL OFFICIAL BRAND COLORS (EXACT HEX) === */
  /* Primary Brand Color: Rotary Royal Blue */
  --color-rotary-blue: #17458f; /* PMS 286C */
  /* Accent Color: Rotary Gold */
  --color-rotary-gold: #f7a81b; /* PMS 130C - OFFICIAL HEX */
  /* === ADDITIONAL ROTARY BRAND COLORS === */
  /* Secondary and accent colors from Rotary's official palette */
  --color-rotary-azure: #0067c8; /* PMS 2175C */
  --color-rotary-sky-blue: #00a2e0; /* PMS 2202C */
  --color-rotary-dark-blue: #003399; /* PMS 661C */

  /* === COULEURS SPÉCIALISÉES ROTARY (LOGOS OFFICIELS) === */
  /* Couleurs pour logos spécialisés et programmes */
  --color-rotary-cranberry: #d41367; /* PMS 214C - Logo Rotaract */
  --color-rotary-cardinal: #e02927; /* PMS 485C - End Polio Now */
  --color-rotary-turquoise: #00adbb; /* PMS 7466C */
  --color-rotary-orange: #ff7600; /* PMS 2018C */
  --color-rotary-violet: #901f93; /* PMS 2070C */
  --color-rotary-lawn-green: #009739; /* PMS 355C */

  /* === COULEURS NEUTRES OFFICIELLES === */
  /* Palette étendue pour designs complexes */
  --color-rotary-pastel-blue: #b9d9eb; /* PMS 290C */
  --color-rotary-moss-green: #a7aca2; /* PMS 7537C */
  --color-rotary-lavender: #c6bcd0; /* PMS 665C */
  --color-rotary-taupe: #d9c89e; /* PMS 7501C */
  --color-rotary-mouse-gray: #9ba4b4; /* PMS 2162C */
  --color-rotary-slate: #657f99; /* PMS 2165C */
  --color-rotary-anthracite: #54565a; /* Cool Gray 11C */
  --color-rotary-tin: #898a8d; /* Cool Gray 8C */
  --color-rotary-light-gray: #b1b1b1; /* Cool Gray 5C */
  --color-rotary-silver: #d0cfcd; /* Cool Gray 2C */
  /* === ACCESSIBILITY-ENHANCED DERIVATIVE COLORS === */
  /* Darker variants for better contrast on light backgrounds or as text */
  --color-rotary-blue-dark: #0f3670;
  --color-rotary-gold-dark: #d68f00; /* Calculated from official #f7a81b */
  --color-rotary-azure-dark: #0055a4;
  --color-rotary-sky-blue-dark: #0088c0;
  /* Lighter variants for use on dark backgrounds or as highlights */
  --color-rotary-blue-light: #2d5ba0;
  --color-rotary-gold-light: #f9b84a; /* Calculated from official #f7a81b */
  --color-rotary-dark-blue-light: #1a4b9c;
  /* === SEMANTIC COLOR ROLES (LIGHT MODE DEFAULTS) === */
  --color-background: #ffffff;
  --color-foreground: #0f172a;
  --color-card: #ffffff;
  --color-card-foreground: #0f172a;
  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;
  /* Primary uses official Rotary Blue */
  --color-primary: var(--color-rotary-blue);
  --color-primary-foreground: #ffffff;
  /* Secondary uses Rotary Gold on Black for maximum contrast (10.57:1 ratio, AAA compliant) */
  --color-secondary: var(--color-rotary-gold);
  --color-secondary-foreground: #000000;
  /* Tertiary uses Rotary Sky Blue */
  --color-tertiary: var(--color-rotary-sky-blue);
  --color-tertiary-foreground: #ffffff;
  /* Muted and accent colors */
  --color-muted: #f8fafc;
  --color-muted-foreground: #64748b;
  --color-accent: var(--color-rotary-gold);
  --color-accent-foreground: var(--color-rotary-gold-dark);
  /* Destructive actions */
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;
  /* Borders and inputs */
  --color-border: #e2e8f0;
  --color-input: #ffffff;
  --color-input-border: #d1d5db;
  --color-ring: var(--color-rotary-blue);
  /* === BORDER RADIUS SYSTEM === */
  --radius: 0.625rem; /* 10px */
  --radius-sm: calc(var(--radius) - 4px); /* 6px */
  --radius-md: calc(var(--radius) - 2px); /* 8px */
  --radius-lg: var(--radius); /* 10px */
  --radius-xl: calc(var(--radius) + 4px); /* 14px */
  /* === TYPOGRAPHY SYSTEM (ROTARY INTERNATIONAL OFFICIAL - FREE FONTS ONLY) === */
  /* PRIMARY FONTS - For headings and titles (Free alternatives per Rotary Guidelines) */
  --font-family-primary: var(--font-open-sans), "Open Sans", Arial, system-ui, -apple-system, sans-serif;
  /* SECONDARY FONTS - For body text, subtitles and captions (Free alternatives per Rotary Guidelines) */
  --font-family-secondary: Georgia, "Times New Roman", serif;
  /* LEGACY SANS - Maintained for compatibility */
  --font-family-sans: var(--font-family-primary);
  /* Arabic Script Font Stack for RTL/i18n (All free fonts) */
  --font-family-arabic: "Traditional Arabic", Amiri, "Scheherazade New", sans-serif;
  /* Font Weights */
  --font-weight-regular: 400;
  --font-weight-semibold: 600;
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  /* Letter Spacing */
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  /* Font Sizes (REM-based for scalability) */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
}
/* === DARK MODE THEME OVERRIDES === */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: #0f172a;
    --color-foreground: #f8fafc;
    --color-card: #1e293b;
    --color-card-foreground: #f8fafc;
    --color-popover: #1e293b;
    --color-popover-foreground: #f8fafc;
    --color-primary: var(--color-rotary-blue);
    --color-primary-foreground: #ffffff;
    --color-secondary: var(--color-rotary-gold);
    --color-secondary-foreground: #000000;
    --color-tertiary: var(--color-rotary-sky-blue-dark);
    --color-tertiary-foreground: #ffffff;
    --color-muted: #1e293b;
    --color-muted-foreground: #94a3b8;
    --color-accent: var(--color-rotary-gold);
    --color-accent-foreground: #0f172a;
    --color-destructive: #ef4444;
    --color-destructive-foreground: #ffffff;
    --color-border: rgba(248, 250, 252, 0.1);
    --color-input: #1e293b;
    --color-input-border: rgba(248, 250, 252, 0.2);
    --color-ring: var(--color-rotary-gold);
  }
}
/* Class-based dark mode for JavaScript control */
.theme-dark {
  --color-background: #0f172a;
  --color-foreground: #f8fafc;
  --color-card: #1e293b;
  --color-card-foreground: #f8fafc;
  --color-popover: #1e293b;
  --color-popover-foreground: #f8fafc;
  --color-primary: var(--color-rotary-blue);
  --color-primary-foreground: #ffffff;
  --color-secondary: var(--color-rotary-gold);
  --color-secondary-foreground: #000000;
  --color-tertiary: var(--color-rotary-sky-blue-dark);
  --color-tertiary-foreground: #ffffff;
  --color-muted: #1e293b;
  --color-muted-foreground: #94a3b8;
  --color-accent: var(--color-rotary-gold);
  --color-accent-foreground: #0f172a;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;
  --color-border: rgba(248, 250, 252, 0.1);
  --color-input: #1e293b;
  --color-input-border: rgba(248, 250, 252, 0.2);
  --color-ring: var(--color-rotary-gold);
}
/* === HIGH CONTRAST MODE SUPPORT === */
@media (prefers-contrast: high) {
  :root {
    --color-border: #000000;
    --color-ring: #000000;
  }
  .theme-dark {
    --color-border: #ffffff;
    --color-ring: #ffffff;
  }
}
/* === RTL & ARABIC LANGUAGE SUPPORT === */
.font-arabic,
[lang="ar"] * {
  font-family: var(--font-family-arabic);
  font-feature-settings: "kern" 1, "liga" 1;
}
[dir="rtl"] {
  text-align: right;
  direction: rtl;
}
[dir="rtl"] .ml-auto { margin-left: 0; margin-right: auto; }
[dir="rtl"] .mr-auto { margin-right: 0; margin-left: auto; }
[dir="rtl"] .pl-4 { padding-left: 0; padding-right: 1rem; }
[dir="rtl"] .pr-4 { padding-right: 0; padding-left: 1rem; }
[lang="ar"] {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* === ACCESSIBILITY ENHANCEMENTS === */
*:focus-visible {
  outline: 2px solid var(--color-ring);
  outline-offset: 2px;
}
/* === UTILITY CLASSES FOR TYPOGRAPHY (ROTARY OFFICIAL - FREE FONTS ONLY) === */

/* === PRIMARY TYPOGRAPHY (Headings & Titles) === */
/* Use Open Sans → Arial (FREE) for headings per Rotary guidelines */
.font-primary {
  font-family: var(--font-family-primary);
}

.h1, .heading-1 {
  font-family: var(--font-family-primary); /* Rotary Primary: Open Sans/Arial (FREE) */
  font-size: var(--text-4xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
}

.h2, .heading-2 {
  font-family: var(--font-family-primary); /* Rotary Primary: Open Sans/Arial (FREE) */
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
}

.h3, .heading-3 {
  font-family: var(--font-family-primary); /* Rotary Primary: Open Sans/Arial (FREE) */
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-tight);
}

/* === SECONDARY TYPOGRAPHY (Body Text & Subtitles) === */
/* Use Georgia → Times New Roman (FREE) for body text per Rotary guidelines */
.font-secondary {
  font-family: var(--font-family-secondary);
}

.body-text, .text-body {
  font-family: var(--font-family-secondary); /* Rotary Secondary: Georgia/Times New Roman (FREE) */
  font-size: var(--text-base);
  font-weight: var(--font-weight-regular);
  line-height: var(--leading-normal);
}

.subtitle, .text-subtitle {
  font-family: var(--font-family-secondary); /* Rotary Secondary: Georgia/Times New Roman (FREE) */
  font-size: var(--text-lg);
  font-weight: var(--font-weight-regular);
  line-height: var(--leading-normal);
}

.caption, .text-caption {
  font-family: var(--font-family-secondary); /* Rotary Secondary: Georgia/Times New Roman (FREE) */
  font-size: var(--text-sm);
  font-weight: var(--font-weight-regular);
  line-height: var(--leading-normal);
}

/* === BUTTON TYPOGRAPHY === */
/* Use primary font for buttons per UI best practices */
.button-text {
  font-family: var(--font-family-primary); /* Rotary Primary for UI elements (FREE) */
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--tracking-wide);
}
/* === NEW UTILITY CLASSES FOR COLORS === */
/* Text colors */
.text-rotary-blue { color: var(--color-rotary-blue); }
.text-rotary-gold { color: var(--color-rotary-gold); }
.text-rotary-azure { color: var(--color-rotary-azure); }
.text-rotary-sky-blue { color: var(--color-rotary-sky-blue); }
.text-rotary-dark-blue { color: var(--color-rotary-dark-blue); }
/* Background colors */
.bg-rotary-blue { background-color: var(--color-rotary-blue); }
.bg-rotary-gold { background-color: var(--color-rotary-gold); }
.bg-rotary-azure { background-color: var(--color-rotary-azure); }
.bg-rotary-sky-blue { background-color: var(--color-rotary-sky-blue); }
.bg-rotary-dark-blue { background-color: var(--color-rotary-dark-blue); }
/* Border colors */
.border-rotary-blue { border-color: var(--color-rotary-blue); }
.border-rotary-gold { border-color: var(--color-rotary-gold); }
.border-rotary-azure { border-color: var(--color-rotary-azure); }
.border-rotary-sky-blue { border-color: var(--color-rotary-sky-blue); }
.border-rotary-dark-blue { border-color: var(--color-rotary-dark-blue); }
/* === CUSTOM COMPONENTS === */
/* === ENHANCED ROTARY BUTTON SYSTEM (WCAG AAA COMPLIANT) === */

/* Primary Button - Rotary Blue on White (9.19:1 ratio, AAA compliant) */
.btn-primary {
  background: linear-gradient(135deg, var(--color-rotary-blue) 0%, var(--color-rotary-blue-dark) 100%);
  color: #ffffff;
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-sm);
  letter-spacing: var(--tracking-wide);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  border: 2px solid var(--color-rotary-blue);
  box-shadow: 0 2px 8px rgba(23, 69, 143, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(23, 69, 143, 0.4);
  background: linear-gradient(135deg, var(--color-rotary-blue-dark) 0%, #0a2440 100%);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 2px 8px rgba(23, 69, 143, 0.3), 0 0 0 3px rgba(23, 69, 143, 0.3);
}

.btn-primary:focus-visible {
  outline: 2px solid var(--color-ring);
  outline-offset: 2px;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(23, 69, 143, 0.3);
  background: linear-gradient(135deg, #0a2440 0%, var(--color-rotary-blue) 100%);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Secondary Button - Rotary Gold on Black (10.57:1 ratio, AAA compliant) */
.btn-secondary {
  background: linear-gradient(135deg, var(--color-rotary-gold) 0%, var(--color-rotary-gold-dark) 100%);
  color: #000000;
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-sm);
  letter-spacing: var(--tracking-wide);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  border: 2px solid var(--color-rotary-gold);
  box-shadow: 0 2px 8px rgba(247, 168, 27, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(247, 168, 27, 0.4);
  background: linear-gradient(135deg, var(--color-rotary-gold-dark) 0%, #b87500 100%);
}

.btn-secondary:hover::before {
  left: 100%;
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 2px 8px rgba(247, 168, 27, 0.3), 0 0 0 3px rgba(247, 168, 27, 0.4);
}

.btn-secondary:focus-visible {
  outline: 2px solid var(--color-ring);
  outline-offset: 2px;
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(247, 168, 27, 0.3);
  background: linear-gradient(135deg, #b87500 0%, var(--color-rotary-gold) 100%);
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Tertiary Button - Rotary Sky Blue on White (20.82:1 ratio, AAA compliant) */
.btn-tertiary {
  background: linear-gradient(135deg, var(--color-rotary-sky-blue) 0%, var(--color-rotary-sky-blue-dark) 100%);
  color: #ffffff;
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-sm);
  letter-spacing: var(--tracking-wide);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  border: 2px solid var(--color-rotary-sky-blue);
  box-shadow: 0 2px 8px rgba(0, 162, 224, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.btn-tertiary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-tertiary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 162, 224, 0.4);
  background: linear-gradient(135deg, var(--color-rotary-sky-blue-dark) 0%, #006b8f 100%);
}

.btn-tertiary:hover::before {
  left: 100%;
}

.btn-tertiary:focus {
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 162, 224, 0.3), 0 0 0 3px rgba(0, 162, 224, 0.4);
}

.btn-tertiary:focus-visible {
  outline: 2px solid var(--color-ring);
  outline-offset: 2px;
}

.btn-tertiary:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0, 162, 224, 0.3);
  background: linear-gradient(135deg, #006b8f 0%, var(--color-rotary-sky-blue) 100%);
}

.btn-tertiary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* === ENHANCED ACCESSIBILITY BUTTON VARIANTS === */

/* High Contrast Primary Button - For maximum accessibility */
.btn-primary-high-contrast {
  background: linear-gradient(135deg, var(--color-rotary-blue) 0%, #000000 100%);
  color: #ffffff;
  border: 3px solid #ffffff;
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-base);
  padding: 0.875rem 2rem;
  box-shadow: 0 4px 12px rgba(23, 69, 143, 0.5);
}

.btn-primary-high-contrast:hover {
  background: linear-gradient(135deg, #000000 0%, var(--color-rotary-blue) 100%);
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(23, 69, 143, 0.6);
}

.btn-primary-high-contrast:focus {
  box-shadow: 0 4px 12px rgba(23, 69, 143, 0.5), 0 0 0 4px rgba(255, 255, 255, 0.8);
}

/* Enhanced Secondary Button with Better Contrast */
.btn-secondary-enhanced {
  background: linear-gradient(135deg, var(--color-rotary-gold) 0%, var(--color-rotary-gold-dark) 100%);
  color: #000000;
  border: 2px solid #000000;
  font-weight: var(--font-weight-semibold);
  padding: 0.75rem 1.5rem;
  box-shadow: 0 2px 8px rgba(247, 168, 27, 0.4);
}

.btn-secondary-enhanced:hover {
  background: linear-gradient(135deg, #000000 0%, var(--color-rotary-gold) 100%);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(247, 168, 27, 0.5);
}

.btn-secondary-enhanced:focus {
  box-shadow: 0 2px 8px rgba(247, 168, 27, 0.4), 0 0 0 3px rgba(0, 0, 0, 0.5);
}

/* === BUTTON SIZE VARIANTS === */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: var(--text-xs);
  border-radius: var(--radius-sm);
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: var(--text-lg);
  border-radius: var(--radius-lg);
}

.btn-xl {
  padding: 1.25rem 2.5rem;
  font-size: var(--text-xl);
  border-radius: var(--radius-xl);
}

/* === BUTTON GROUP ENHANCEMENTS === */
.btn-group .btn-primary,
.btn-group .btn-secondary,
.btn-group .btn-tertiary {
  border-radius: 0;
  margin: 0;
  border-left-width: 0;
  border-right-width: 1px;
  box-shadow: none;
}

.btn-group .btn-primary:first-child,
.btn-group .btn-secondary:first-child,
.btn-group .btn-tertiary:first-child {
  border-left-width: 2px;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.btn-group .btn-primary:last-child,
.btn-group .btn-secondary:last-child,
.btn-group .btn-tertiary:last-child {
  border-right-width: 2px;
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

/* === LOADING STATE === */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: button-loading-spinner 1s ease-in-out infinite;
}

@keyframes button-loading-spinner {
  from {
    transform: translate(-50%, -50%) rotate(0turn);
  }
  to {
    transform: translate(-50%, -50%) rotate(1turn);
  }
}
/* Rotary-Themed Card */
.card {
  background-color: var(--color-card);
  color: var(--color-card-foreground);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
/* Navigation Menu */
.nav-menu {
  display: flex;
  list-style: none;
  gap: 1rem;
  padding: 0;
  margin: 0;
}
.nav-link {
  color: var(--color-foreground);
  font-weight: var(--font-weight-semibold);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease-in-out;
}
.nav-link:hover {
  background-color: var(--color-rotary-blue-light);
  color: var(--color-primary-foreground);
}
/* === PRINT STYLES === */
@media print {
  * {
    background-color: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  .btn-primary,
  .btn-secondary,
  .btn-tertiary {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  .no-print {
    display: none !important;
  }
}
