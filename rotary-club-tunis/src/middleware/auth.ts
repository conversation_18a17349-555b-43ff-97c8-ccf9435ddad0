import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';

// Extended user interface for type safety
interface ExtendedUser {
  role?: 'admin' | 'editor' | 'volunteer';
  id?: string;
  [key: string]: unknown;
}

// Security headers for Tunisia network
const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
};

// Rate limiting for Tunisia network connectivity
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100;
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitStore.get(ip);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitStore.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  userLimit.count++;
  return true;
}

export async function authMiddleware(request: NextRequest) {
  const response = NextResponse.next();

  // Add security headers
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Get client IP for rate limiting
  const forwardedFor = request.headers.get('x-forwarded-for');
  const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : 'unknown';

  // Apply rate limiting
  if (!checkRateLimit(ip)) {
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { status: 429 }
    );
  }

  // Get the pathname
  const { pathname } = request.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = [
    '/api/graphql',
    '/api/graphql-playground',
    '/login',
    '/register',
    '/forgot-password',
  ];

  // Admin routes that require authentication
  const adminRoutes = [
    '/admin',
    '/api/users',
  ];

  // Check if the route requires authentication
  const requiresAuth = adminRoutes.some(route => pathname.startsWith(route));
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  if (requiresAuth && !isPublicRoute) {
    try {
      // For now, check if user is authenticated via session
      // This will be enhanced with proper JWT token validation
      const payloadToken = request.cookies.get('payload-token')?.value;

      if (!payloadToken) {
        return NextResponse.redirect(new URL('/login', request.url));
      }

      // TODO: Implement proper JWT token validation with Payload
      // For now, we'll rely on Payload's built-in authentication
      const extendedUser = { role: 'volunteer', id: 'temp' } as ExtendedUser;

      // Check role-based access for admin routes
      if (pathname.startsWith('/admin') && extendedUser.role !== 'admin' && extendedUser.role !== 'editor') {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        );
      }

      // Add user info to response headers for client-side access
      response.headers.set('x-user-id', extendedUser.id || '');
      response.headers.set('x-user-role', extendedUser.role || 'volunteer');

    } catch (error) {
      console.error('Authentication error:', error);
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  return response;
}

// Export the middleware configuration
export const middlewareConfig = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};