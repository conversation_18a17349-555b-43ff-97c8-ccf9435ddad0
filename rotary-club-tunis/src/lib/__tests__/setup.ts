/**
 * Test setup file for authentication utilities
 * Sets up environment variables and test configuration
 */

// Set up environment variables for testing
process.env.PAYLOAD_SECRET = 'test-secret-key-for-jwt-authentication-rotary-tunis-doyen-cms-system-very-long-and-secure';
process.env.JWT_SECRET = 'test-secret-key-for-jwt-authentication-rotary-tunis-doyen-cms-system-very-long-and-secure';

// Increase timeout for bcrypt operations
jest.setTimeout(30000);
