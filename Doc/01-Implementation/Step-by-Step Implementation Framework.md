# Rotary Club Tunis Doyen CMS - Step-by-Step Implementation Framework

## 🎯 Project Overview

**Objective**: Build a trilingual (French/Arabic/English) CMS with automated workflows, GDPR compliance, and mobile optimization for Rotary Club Tunis Doyen volunteers.

**Timeline**: 8-10 weeks  
**Team Size**: 2-3 developers + 1 stakeholder representative

---

## 📋 Phase 0: Foundation & Planning (Week 1)

### Day 1-2: Stakeholder Alignment

**Duration**: 2 days
**Dependencies**: None
**Owner**: Project Manager + Stakeholders

**Tasks**:

1. **Requirements Finalization**
   - Confirm trilingual CMS with automated workflows
   - Validate GDPR compliance requirements for French members
   - Define mobile-first approach for Tunisian volunteers
   - Document stakeholder success metrics

2. **Architecture Decision Making**
   - Select Payload CMS v3 + Next.js 14 + Vercel + Atlas stack
   - Choose Backblaze B2 for cost-effective file storage
   - Define Arabic RTL implementation strategy
   - Establish auto-draft sync system requirements

3. **Environment Naming & Structure**
   - Define GitHub repository structure (private, protected branches)
   - Establish Vercel project naming convention
   - Plan MongoDB Atlas cluster configuration (M0, IP-restricted)
   - Document secrets management strategy (PAYLOAD_SECRET, MONGODB_URI)

**Deliverables**:
- ✅ Signed requirements document
- ✅ Technology stack decision matrix
- ✅ Environment setup specifications
- ✅ Risk assessment with mitigation strategies

### Day 3-5: Infrastructure Planning

**Duration**: 3 days
**Dependencies**: Requirements approval
**Owner**: DevOps/Backend Developer

**Tasks**:

1. **GitHub Repository Setup**
   - Create private repository with branch protection rules
   - Configure protected main branch (require PR reviews)
   - Set up development and staging branches
   - Enable GitHub Actions for CI/CD

2. **Vercel Project Configuration**
   - Plan Pro subscription with warm serverless functions
   - Configure custom domain: cms.rotary-tunis.tn
   - Set up environment variable management
   - Plan deployment strategy (preview deployments)

3. **Database & Storage Planning**
   - MongoDB Atlas M0 cluster configuration
   - IP whitelist strategy for Vercel serverless
   - Backblaze B2 bucket setup with S3-compatible access
   - Client-side encryption configuration

**Deliverables**:
- ✅ Infrastructure specification document
- ✅ Security configuration guide
- ✅ Backup and recovery strategy
- ✅ Cost optimization plan

---

## 📋 Phase 1: Core Infrastructure (Week 2)

### Day 1-2: Stakeholder Alignment

**Duration**: 2 days  
**Dependencies**: None  
**Owner**: Project Manager + Stakeholders

**Tasks**:

1. **Stakeholder Workshop** (2 hours)
   - Define success metrics: "Reduce event publishing time by 50%"
   - Identify user personas: Volunteers, Editors, Admins
   - Confirm trilingual priorities: French (primary) → Arabic → English

2. **Create RACI Matrix**
   - Responsible: Development team
   - Accountable: Project sponsor
   - Consulted: Volunteer representatives
   - Informed: Rotary Club board

3. **Risk Assessment**
   - Technical risks: Arabic RTL complexity, auto-sync loops
   - Business risks: Volunteer adoption, GDPR compliance gaps
   - Mitigation strategies documented

**Deliverables**:

- ✅ Signed scope document
- ✅ RACI matrix
- ✅ Risk register with mitigation plans

### Day 3-5: Infrastructure Setup

**Duration**: 3 days  
**Dependencies**: Scope approval  
**Owner**: DevOps/Backend Developer

**Tasks**:

1. **Vercel Pro Configuration** (Day 3)

   ```bash
# Setup commands

   vercel login
   vercel init rotary-tunis-cms
   vercel env add PAYLOAD_SECRET
   vercel env add MONGODB_URI
```
   - Enable warm serverless functions (min 1 instance)
   - Configure custom domain: cms.rotary-tunis.tn
   
2. **MongoDB Atlas Setup** (Day 4)
   - Create cluster: `rotary-tunis-production`
   - Database user: `cms_admin@rotary-tunis` (least privilege)
   - IP whitelist: Vercel serverless ranges
   - Daily backups enabled (30-day retention)
   
3. **Backblaze B2 Storage** (Day 5)
   - Bucket: `rotary-tunis-media`
   - S3-compatible access keys
   - Client-side encryption enabled
   - Lifecycle rule: Delete after 365 days

**Testing Criteria**:
- [ ] Database connection test passes
- [ ] File upload/download works from Vercel
- [ ] Backup restoration successful (test with dummy data)

**Deliverables**:
- ✅ Live infrastructure endpoints
- ✅ Environment variables configured
- ✅ Backup/restore procedure documented

---

## 📋 Phase 2: Core CMS Development (Weeks 2-4)

### Week 2: Payload CMS Foundation

**Day 1-2: Base Installation**
**Owner**: Backend Developer

1. **Install Payload CMS**
   ```bash
npx create-payload-app rotary-cms
   cd rotary-cms
   npm install @payloadcms/richtext-slate
   npm install @payloadcms/plugin-cloud-storage
```

2. **Configure Collections Structure**

   ```javascript
// collections/Events.js
   const Events = {
     slug: 'events',
     fields: [
       { name: 'title', type: 'text', localized: true, required: true },
       { name: 'date', type: 'date', required: true },
       { name: 'location', type: 'text', localized: true },
       { name: 'description', type: 'richText', localized: true },
       { name: 'featuredImage', type: 'upload', relationTo: 'media' },
       { name: 'status', type: 'select', options: ['draft', 'published', 'archived'] }
     ]
   }
```

**Day 3-5: Localization Setup**

3. **Configure Field-Level Localization**
   ```javascript
// payload.config.js
   export default buildConfig({
     localization: {
       locales: ['fr', 'ar', 'en'],
       defaultLocale: 'fr',
       fallback: true
     }
   })
```

4. **Arabic RTL Configuration**

   ```css
/*Custom CSS for Arabic RTL*/
   [dir="rtl"] .rich-text {
     text-align: right;
     direction: rtl;
   }
```

**Testing**: Create sample event in all three languages

### Week 3: Advanced Features

**Day 1-3: Auto-Draft Sync System**
**Owner**: Backend Developer

1. **Implement `syncArabicAfterCreate` Hook**
   ```javascript
// hooks/syncArabicAfterCreate.js
   const syncArabicAfterCreate = async ({ doc, req }) => {
     // Safeguard: Skip if already Arabic or Arabic version exists
     if (req.locale === 'ar' || doc.arabic_draft_created) return;
     
     try {
       const arabicDraft = await req.payload.create({
         collection: 'events',
         data: {
           ...doc,
           title: `[مسودة] ${doc.title}`,
           status: 'draft',
           original_language: req.locale,
           arabic_draft_created: true
         },
         locale: 'ar'
       });
     } catch (error) {
       req.payload.logger.error('Arabic draft sync failed:', error);
     }
   };
```

2. **Recursion Prevention Logic**
   - Flag: `arabic_draft_created: boolean`
   - Validation: Skip hook if locale is 'ar'
   - Error handling: Log failures, don't block main operation

**Day 4-5: Role-Based Access Control**

3. **Configure RBAC**

   ```javascript
// collections/Users.js
   const Users = {
     auth: true,
     fields: [
       { name: 'role', type: 'select',
         options: ['admin', 'editor', 'volunteer'],
         defaultValue: 'volunteer' },
       { name: 'languagePreference', type: 'select',
         options: ['fr', 'ar', 'en'], defaultValue: 'fr' }
     ],
     access: {
       read: ({ req: { user } }) => {
         if (user.role === 'admin') return true;
         if (user.role === 'editor') return { role: { not_equals: 'admin' } };
         return { id: { equals: user.id } };
       }
     }
   }
```

### Week 4: Zero-Exposure Admin Interface

**Day 1-3: Custom Admin Dashboard**
**Owner**: Frontend Developer

1. **Volunteer-Friendly Interface**
   ```javascript
// admin/components/VolunteerDashboard.js
   const VolunteerDashboard = ({ user }) => {
     if (user.role === 'volunteer') {
       return (
         <div className="volunteer-dashboard">
           <h1>مرحباً {user.name}</h1>
           <QuickActions>
             <Button href="/admin/collections/events/create">
               إضافة فعالية جديدة
             </Button>
             <Button href="/admin/collections/minutes/create">
               إضافة محضر اجتماع
             </Button>
           </QuickActions>
         </div>
       );
     }
     return <DefaultAdminPanel />;
   };
```

2. **Hide Technical Menus**
   - Remove API access for volunteers
   - Hide system collections (Users, Media metadata)
   - Redirect volunteers to Events list by default

**Day 4-5: Testing & Validation**

- Role switching tests
- Arabic RTL rendering tests
- Auto-sync edge cases (rapid edits, network failures)

**Deliverables**:

- ✅ Functional CMS with all collections
- ✅ Working auto-Arabic draft sync
- ✅ Role-based permissions enforced
- ✅ Volunteer-friendly admin interface

---

## 📋 Phase 3: Frontend Development (Weeks 5-6)

### Week 5: Next.js Frontend

**Day 1-2: Project Setup**
**Owner**: Frontend Developer

1. **Initialize Next.js 14 with App Router**

   ```bash
npx create-next-app@latest rotary-frontend --app --tailwind --typescript
   cd rotary-frontend
   npm install next-intl sharp
```

2. **Configure Internationalization**
   ```javascript
// middleware.js
   import createMiddleware from 'next-intl/middleware';
   export default createMiddleware({
     locales: ['fr', 'ar', 'en'],
     defaultLocale: 'fr',
     localePrefix: 'always'
   });
```

**Day 3-5: Trilingual UI**

3. **Localized Routing Structure**

   ```
app/
   ├── [locale]/
   │   ├── evenements/page.js (French)
   │   ├── فعاليات/page.js (Arabic)
   │   ├── events/page.js (English)
   │   └── layout.js
```

4. **Arabic RTL Support**
   ```css
/* globals.css */
   [dir="rtl"] {
     direction: rtl;
     text-align: right;
   }
   
   @import url('https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;700&display=swap');
   
   .arabic {
     font-family: 'Noto Naskh Arabic', serif;
   }
```

### Week 6: Performance & Accessibility

**Day 1-2: Image Optimization**

1. **Sharp/WebP Integration**

   ```javascript
// next.config.js
   module.exports = {
     images: {
       formats: ['image/webp', 'image/avif'],
       deviceSizes: [640, 768, 1024, 1280, 1600],
       loader: 'custom',
       loaderFile: './imageLoader.js'
     }
   }
```

**Day 3-5: Accessibility Audit**

2. **WCAG 2.1 AA Compliance**
   ```javascript
// components/EventCard.js
   const EventCard = ({ event }) => (
     <article 
       className="event-card" 
       role="article"
       aria-labelledby={`event-${event.id}-title`}
     >
       <h2 id={`event-${event.id}-title`}>{event.title}</h2>
       <img 
         src={event.image} 
         alt={event.altText || 'صورة الفعالية'} 
         loading="lazy"
       />
     </article>
   );
```

3. **Arabic Screen Reader Testing**
   - Test with NVDA + Arabic voice
   - Verify RTL navigation (Tab order)
   - Validate ARIA labels in Arabic

**Mobile Testing Protocol**:

- Device: Xiaomi Redmi 9A (Android 10)
- Network: 3G (Tunisian ISP simulation)
- Target: <8s load time, 48x48px touch targets

**Deliverables**:

- ✅ Responsive trilingual frontend
- ✅ Arabic RTL fully functional
- ✅ WCAG AA compliant
- ✅ Mobile-optimized (<8s load time)

---

## 📋 Phase 4: Security & Compliance (Week 7)

### Day 1-3: GDPR Implementation

**Owner**: Backend Developer + Legal Consultant

1. **Consent Management System**

   ```javascript
// components/CookieConsent.js
   const CookieConsent = () => {
     const [consent, setConsent] = useState(null);

     const handleConsent = (accepted) => {
       // Store minimal consent data
       localStorage.setItem('gdpr-consent', JSON.stringify({
         accepted,
         timestamp: new Date().toISOString(),
         version: '1.0'
       }));
       setConsent(accepted);
     };
     
     return (
       <div className="cookie-banner" role="dialog" aria-live="polite">
         <p>نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك</p>
         <button onClick={() => handleConsent(true)}>موافق</button>
         <button onClick={() => handleConsent(false)}>رفض</button>
       </div>
     );
   };
```

2. **Data Subject Request System**
   ```javascript
// api/gdpr/data-request.js
   export default async function handler(req, res) {
     const { email, requestType } = req.body;
     
     switch (requestType) {
       case 'export':
         // Generate user data export
         break;
       case 'delete':
         // Schedule data deletion
         break;
       case 'rectification':
         // Allow data correction
         break;
     }
   }
```

### Day 4-5: Security Hardening

3. **Secrets Rotation Protocol**

   ```bash
# Monthly rotation script

   #!/bin/bash
   NEW_SECRET=$(openssl rand -base64 32)
   vercel env rm PAYLOAD_SECRET
   vercel env add PAYLOAD_SECRET $NEW_SECRET
```

4. **Backup Automation**
   ```javascript
// scripts/backup.js
   const backup = async () => {
     const timestamp = new Date().toISOString().split('T')[0];
     await exec(`mongodump --uri=${MONGODB_URI} --out=./backups/${timestamp}`);
     await uploadToB2(`./backups/${timestamp}`);
   };
   
   // Schedule: Every night at 2 AM Tunis time
   cron.schedule('0 2 * * *', backup);
```

**Security Audit Checklist**:

- [ ] SQL injection prevention (MongoDB injection)
- [ ] XSS protection (Arabic text sanitization)
- [ ] CSRF tokens implemented
- [ ] Rate limiting on API endpoints
- [ ] Admin panel 2FA enabled

---

## 📋 Phase 5: Testing & Quality Assurance (Week 8)

### Day 1-2: Automated Testing

**Owner**: QA Engineer/Developer

1. **Unit Tests**

   ```javascript
// tests/hooks/syncArabicAfterCreate.test.js
   describe('Arabic Auto-Sync', () => {
     test('creates Arabic draft after French event', async () => {
       const frenchEvent = await createEvent({
         title: 'Événement Test',
         locale: 'fr'
       });

       const arabicDraft = await findArabicDraft(frenchEvent.id);
       expect(arabicDraft.title).toContain('[مسودة]');
       expect(arabicDraft.status).toBe('draft');
     });
     
     test('prevents recursion loop', async () => {
       const arabicEvent = await createEvent({ 
         title: 'فعالية تجريبية', 
         locale: 'ar' 
       });
       
       const duplicates = await findDuplicateArabicDrafts(arabicEvent.id);
       expect(duplicates).toHaveLength(0);
     });
   });
```

2. **Integration Tests**
   ```javascript
// tests/api/events.test.js
   describe('Events API', () => {
     test('volunteer can create event', async () => {
       const response = await request(app)
         .post('/api/events')
         .set('Authorization', `Bearer ${volunteerToken}`)
         .send(eventData);
       
       expect(response.status).toBe(201);
       expect(response.body.status).toBe('draft');
     });
   });
```

### Day 3-4: Localization Testing

3. **Language Cascade Testing**

   ```javascript
// tests/localization.test.js
   test('language fallback works correctly', () => {
     const event = {
       title: { fr: 'Événement', en: 'Event' }, // No Arabic
       description: { fr: 'Description', ar: 'وصف', en: 'Description' }
     };

     const arabicView = renderEventInLocale(event, 'ar');
     expect(arabicView.title).toBe('Event'); // Falls back to English
     expect(arabicView.description).toBe('وصف'); // Uses Arabic
   });
```

4. **RTL Visual Testing**
   - Screenshot comparisons (Percy.io or similar)
   - Form input alignment
   - Modal dialog positioning
   - Navigation menu behavior

### Day 5: User Acceptance Testing

5. **Volunteer Testing Session**
   - 5 volunteers test core workflows
   - Screen recordings for usability analysis
   - Feedback form in French/Arabic
   
   **Test Scenarios**:
   - Create and publish an event
   - Upload meeting minutes
   - Switch between languages
   - Use mobile interface

**Quality Gates**:
- [ ] 95% test coverage on critical paths
- [ ] Zero accessibility violations (axe-core)
- [ ] <8s load time on 3G mobile
- [ ] Volunteer approval rating >4/5

---

## 📋 Phase 6: Deployment & Launch (Weeks 9-10)

### Week 9: Staging Deployment

**Day 1-2: Staging Environment**

1. **Deploy to Vercel Staging**
   ```bash
# Deploy staging branch
   git checkout staging
   vercel --env=staging
   vercel alias rotary-cms-staging.vercel.app
```

2. **Data Migration Strategy**

   ```javascript
// scripts/migrate.js
   const migrateExistingData = async () => {
     const existingEvents = await fetchFromOldSystem();

     for (const event of existingEvents) {
       await payload.create({
         collection: 'events',
         data: transformLegacyEvent(event),
         locale: detectLanguage(event.title)
       });
     }
   };
```

**Day 3-5: Final Testing**
- Load testing (Artillery.io): 100 concurrent users
- Security penetration testing
- Backup/restore drill validation
- GDPR compliance audit

### Week 10: Production Launch

**Day 1-2: Production Deployment**

1. **Go-Live Checklist**
   ```markdown
## Pre-Launch Checklist
   - [ ] All tests passing
   - [ ] Security scan completed
   - [ ] Backups tested and verified
   - [ ] DNS records updated
   - [ ] SSL certificates valid
   - [ ] Monitoring alerts configured
   - [ ] Rollback plan documented
```

2. **Launch Window**: Sunday 2 AM Tunis Time
   - Minimal user activity
   - Support team on standby
   - Rollback ready within 15 minutes

**Day 3-5: Post-Launch**

3. **Documentation Handover**

   ```
Documentation Package:
   ├── User Guides/
   │   ├── volunteer-guide-fr.pdf
   │   ├── volunteer-guide-ar.pdf
   │   └── admin-manual-en.pdf
   ├── Technical Docs/
   │   ├── deployment-guide.md
   │   ├── troubleshooting.md
   │   └── api-documentation.md
   └── Videos/
       ├── event-creation-tutorial-fr.mp4
       └── arabic-rtl-demo.mp4
```

4. **Training Sessions**
   - 2-hour Zoom session for volunteers (French)
   - 1-hour admin training session
   - Record all sessions for future reference

**Launch Success Criteria**:
- [ ] Zero critical bugs in first 48 hours
- [ ] 100% volunteer activation within 1 week
- [ ] First event published successfully in all languages
- [ ] Mobile usage >60% (analytics validation)

---

## 📋 Phase 7: Post-Launch & Maintenance

### Month 1: Monitoring & Optimization

**Week 1-2: Performance Monitoring**
- Setup Vercel Analytics + Google Analytics
- Monitor Core Web Vitals
- Track language usage patterns
- Measure volunteer engagement

**Week 3-4: Feedback Integration**
- Collect volunteer feedback (Typeform survey)
- Analyze user behavior (Hotjar/LogRocket)
- Prioritize enhancement requests
- Plan first iteration

### Ongoing Maintenance Protocol

**Monthly Tasks**:
- [ ] Security updates (npm audit, dependency updates)
- [ ] Performance review (Core Web Vitals)
- [ ] Backup verification (test restore)
- [ ] Analytics review (usage patterns)

**Quarterly Tasks**:
- [ ] GDPR compliance audit
- [ ] Security penetration test
- [ ] Volunteer satisfaction survey
- [ ] Blueprint update with lessons learned

**Annual Tasks**:
- [ ] Full security audit by external firm
- [ ] Infrastructure cost optimization
- [ ] Technology stack evaluation
- [ ] Strategic roadmap planning

---

## 🎯 Key Success Metrics

### Technical KPIs
| Metric | Target | Measurement |
|--------|--------|-------------|
| Page Load Time | <8s on 3G | WebPageTest |
| Uptime | 99.5% | Vercel Analytics |
| Arabic RTL Accuracy | 100% | Manual QA |
| Mobile Usage | >60% | Google Analytics |

### Business KPIs
| Metric | Target | Measurement |
|--------|--------|-------------|
| Event Publishing Time | 50% reduction | User timing |
| Volunteer Adoption | 100% in 2 weeks | Admin analytics |
| Content in Arabic | 80% coverage | CMS reports |
| User Satisfaction | >4.5/5 | Quarterly survey |

### Compliance KPIs
| Metric | Target | Measurement |
|--------|--------|-------------|
| GDPR Compliance | 100% | External audit |
| Accessibility | WCAG AA | axe-core |
| Security Vulnerabilities | Zero critical | Monthly scans |
| Data Backup Success | 100% | Automated tests |

---

## 🚨 Risk Mitigation Strategies

### Technical Risks
| Risk | Probability | Impact | Mitigation |
|------|-------------|--------|------------|
| Arabic RTL Issues | Medium | High | Extensive testing, RTL expert consultation |
| Auto-sync Recursion | Low | High | Safeguards, monitoring, circuit breakers |
| Mobile Performance | Medium | Medium | Progressive enhancement, lazy loading |
| Third-party Failures | Low | Medium | Fallback systems, vendor SLAs |

### Business Risks
| Risk | Probability | Impact | Mitigation |
|------|-------------|--------|------------|
| Volunteer Resistance | Medium | High | Training, gradual rollout, support |
| Content Migration Issues | Low | Medium | Staged migration, rollback plan |
| GDPR Non-compliance | Low | High | Legal review, compliance testing |
| Budget Overrun | Low | Medium | Fixed-price contracts, scope control |

---

## 📞 Support & Escalation

### Support Tiers
1. **Level 1**: Volunteer self-service (documentation, videos)
2. **Level 2**: Admin assistance (Rotary admin team)
3. **Level 3**: Technical support (development team)

### Contact Matrix
| Issue Type | Contact | Response Time |
|------------|---------|---------------|
| Password reset | <EMAIL> | 24 hours |
| Technical bug | <EMAIL> | 4 hours |
| Security incident | <EMAIL> | 1 hour |
| GDPR request | <EMAIL> | 72 hours |

---

*This framework ensures systematic implementation while maintaining focus on the unique trilingual, cultural, and technical requirements of Rotary Club Tunis Doyen.*# Rotary Club Tunis Doyen CMS - Step-by-Step Implementation Framework

## 🎯 Project Overview
**Objective**: Build a trilingual (French/Arabic/English) CMS with automated workflows, GDPR compliance, and mobile optimization for Rotary Club Tunis Doyen volunteers.

**Timeline**: 8-10 weeks  
**Team Size**: 2-3 developers + 1 stakeholder representative

---

## 📋 Phase 1: Foundation & Planning (Week 1)

### Day 1-2: Stakeholder Alignment
**Duration**: 2 days  
**Dependencies**: None  
**Owner**: Project Manager + Stakeholders

**Tasks**:
1. **Stakeholder Workshop** (2 hours)
   - Define success metrics: "Reduce event publishing time by 50%"
   - Identify user personas: Volunteers, Editors, Admins
   - Confirm trilingual priorities: French (primary) → Arabic → English
   
2. **Create RACI Matrix**
   - Responsible: Development team
   - Accountable: Project sponsor
   - Consulted: Volunteer representatives
   - Informed: Rotary Club board

3. **Risk Assessment**
   - Technical risks: Arabic RTL complexity, auto-sync loops
   - Business risks: Volunteer adoption, GDPR compliance gaps
   - Mitigation strategies documented

**Deliverables**:
- ✅ Signed scope document
- ✅ RACI matrix
- ✅ Risk register with mitigation plans

### Day 3-5: Infrastructure Setup
**Duration**: 3 days  
**Dependencies**: Scope approval  
**Owner**: DevOps/Backend Developer

**Tasks**:
1. **Vercel Pro Configuration** (Day 3)
   ```bash
# Setup commands
   vercel login
   vercel init rotary-tunis-cms
   vercel env add PAYLOAD_SECRET
   vercel env add MONGODB_URI
```

- Enable warm serverless functions (min 1 instance)
- Configure custom domain: cms.rotary-tunis.tn

2. **MongoDB Atlas Setup** (Day 4)
   - Create cluster: `rotary-tunis-production`
   - Database user: `cms_admin@rotary-tunis` (least privilege)
   - IP whitelist: Vercel serverless ranges
   - Daily backups enabled (30-day retention)

3. **Backblaze B2 Storage** (Day 5)
   - Bucket: `rotary-tunis-media`
   - S3-compatible access keys
   - Client-side encryption enabled
   - Lifecycle rule: Delete after 365 days

**Testing Criteria**:

- [ ] Database connection test passes
- [ ] File upload/download works from Vercel
- [ ] Backup restoration successful (test with dummy data)

**Deliverables**:

- ✅ Live infrastructure endpoints
- ✅ Environment variables configured
- ✅ Backup/restore procedure documented

---

## 📋 Phase 2: Core CMS Development (Weeks 2-4)

### Week 2: Payload CMS Foundation

**Day 1-2: Base Installation**
**Owner**: Backend Developer

1. **Install Payload CMS**

   ```bash
npx create-payload-app rotary-cms
   cd rotary-cms
   npm install @payloadcms/richtext-slate
   npm install @payloadcms/plugin-cloud-storage
```

2. **Configure Collections Structure**

   ```javascript
// collections/Events.js
   const Events = {
     slug: 'events',
     fields: [
       { name: 'title', type: 'text', localized: true, required: true },
       { name: 'date', type: 'date', required: true },
       { name: 'location', type: 'text', localized: true },
       { name: 'description', type: 'richText', localized: true },
       { name: 'featuredImage', type: 'upload', relationTo: 'media' },
       { name: 'status', type: 'select', options: ['draft', 'published', 'archived'] }
     ]
   }
```

**Day 3-5: Localization Setup**

3. **Configure Field-Level Localization**

   ```javascript
// payload.config.js
   export default buildConfig({
     localization: {
       locales: ['fr', 'ar', 'en'],
       defaultLocale: 'fr',
       fallback: true
     }
   })
```

4. **Arabic RTL Configuration**

   ```css
/* Custom CSS for Arabic RTL */
   [dir="rtl"] .rich-text {
     text-align: right;
     direction: rtl;
   }
```

**Testing**: Create sample event in all three languages

### Week 3: Advanced Features

**Day 1-3: Auto-Draft Sync System**
**Owner**: Backend Developer

1. **Implement `syncArabicAfterCreate` Hook** (Fixed - No Spread Operator)

   ```javascript
// hooks/syncArabicAfterCreate.js
   const syncArabicAfterCreate = async ({ doc, req, operation }) => {
     // CRITICAL GUARD: Only trigger on new documents, not updates
     if (operation !== 'create' || req.locale === 'ar' || doc.arabic_draft_created) return;

     try {
       // SAFE FIELD SELECTION: Explicitly select fields to avoid data corruption
       const safeData = {
         title: `[مسودة] ${doc.title}`,
         eventDate: doc.eventDate,
         location: doc.location,
         description: doc.description,
         areasOfFocus: doc.areasOfFocus,
         impactMetrics: doc.impactMetrics,
         gallery: doc.gallery,
         status: 'draft',
         original_language: req.locale,
         arabic_draft_created: true
       };

       const arabicDraft = await req.payload.create({
         collection: 'events',
         data: safeData,
         locale: 'ar'
       });

       req.payload.logger.info(`✅ Created Arabic draft for: ${doc.title}`);
     } catch (error) {
       req.payload.logger.error('❌ Arabic draft sync failed:', error);
     }
   };
```

2. **Recursion Prevention Logic**
   - Flag: `arabic_draft_created: boolean`
   - Validation: Skip hook if locale is 'ar'
   - Error handling: Log failures, don't block main operation

**Day 4-5: Role-Based Access Control**

3. **Configure RBAC**

   ```javascript
// collections/Users.js
   const Users = {
     auth: true,
     fields: [
       { name: 'role', type: 'select', 
         options: ['admin', 'editor', 'volunteer'],
         defaultValue: 'volunteer' },
       { name: 'languagePreference', type: 'select',
         options: ['fr', 'ar', 'en'], defaultValue: 'fr' }
     ],
     access: {
       read: ({ req: { user } }) => {
         if (user.role === 'admin') return true;
         if (user.role === 'editor') return { role: { not_equals: 'admin' } };
         return { id: { equals: user.id } };
       }
     }
   }
```

### Week 4: Zero-Exposure Admin Interface

**Day 1-3: Custom Admin Dashboard**
**Owner**: Frontend Developer

1. **Volunteer-Friendly Interface**

   ```javascript
// admin/components/VolunteerDashboard.js
   const VolunteerDashboard = ({ user }) => {
     if (user.role === 'volunteer') {
       return (
         <div className="volunteer-dashboard">
           <h1>مرحباً {user.name}</h1>
           <QuickActions>
             <Button href="/admin/collections/events/create">
               إضافة فعالية جديدة
             </Button>
             <Button href="/admin/collections/minutes/create">
               إضافة محضر اجتماع
             </Button>
           </QuickActions>
         </div>
       );
     }
     return <DefaultAdminPanel />;
   };
```

2. **Hide Technical Menus**
   - Remove API access for volunteers
   - Hide system collections (Users, Media metadata)
   - Redirect volunteers to Events list by default

**Day 4-5: Testing & Validation**

- Role switching tests
- Arabic RTL rendering tests
- Auto-sync edge cases (rapid edits, network failures)

**Deliverables**:

- ✅ Functional CMS with all collections
- ✅ Working auto-Arabic draft sync
- ✅ Role-based permissions enforced
- ✅ Volunteer-friendly admin interface

---

## 📋 Phase 3: Frontend Development (Weeks 5-6)

### Week 5: Next.js Frontend

**Day 1-2: Project Setup**
**Owner**: Frontend Developer

1. **Initialize Next.js 14 with App Router**

   ```bash
npx create-next-app@latest rotary-frontend --app --tailwind --typescript
   cd rotary-frontend
   npm install next-intl sharp
```

2. **Configure Internationalization**

   ```javascript
// middleware.js
   import createMiddleware from 'next-intl/middleware';
   export default createMiddleware({
     locales: ['fr', 'ar', 'en'],
     defaultLocale: 'fr',
     localePrefix: 'always'
   });
```

**Day 3-5: Trilingual UI**

3. **Localized Routing Structure**

   ```
app/
   ├── [locale]/
   │   ├── evenements/page.js (French)
   │   ├── فعاليات/page.js (Arabic)
   │   ├── events/page.js (English)
   │   └── layout.js
```

4. **Arabic RTL Support**

   ```css
/* globals.css */
   [dir="rtl"] {
     direction: rtl;
     text-align: right;
   }
   
   @import url('https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;700&display=swap');
   
   .arabic {
     font-family: 'Noto Naskh Arabic', serif;
   }
```

### Week 6: Performance & Accessibility

**Day 1-2: Image Optimization**

1. **Sharp/WebP Integration**

   ```javascript
// next.config.js
   module.exports = {
     images: {
       formats: ['image/webp', 'image/avif'],
       deviceSizes: [640, 768, 1024, 1280, 1600],
       loader: 'custom',
       loaderFile: './imageLoader.js'
     }
   }
```

**Day 3-5: Accessibility Audit**

2. **WCAG 2.1 AA Compliance**

   ```javascript
// components/EventCard.js
   const EventCard = ({ event }) => (
     <article 
       className="event-card" 
       role="article"
       aria-labelledby={`event-${event.id}-title`}
     >
       <h2 id={`event-${event.id}-title`}>{event.title}</h2>
       <img 
         src={event.image} 
         alt={event.altText || 'صورة الفعالية'} 
         loading="lazy"
       />
     </article>
   );
```

3. **Arabic Screen Reader Testing**
   - Test with NVDA + Arabic voice
   - Verify RTL navigation (Tab order)
   - Validate ARIA labels in Arabic

**Mobile Testing Protocol**:

- Device: Xiaomi Redmi 9A (Android 10)
- Network: 3G (Tunisian ISP simulation)
- Target: <8s load time, 48x48px touch targets

**Deliverables**:

- ✅ Responsive trilingual frontend
- ✅ Arabic RTL fully functional
- ✅ WCAG AA compliant
- ✅ Mobile-optimized (<8s load time)

---

## 📋 Phase 4: Security & Compliance (Week 7)

### Day 1-3: GDPR Implementation

**Owner**: Backend Developer + Legal Consultant

1. **Consent Management System**

   ```javascript
// components/CookieConsent.js
   const CookieConsent = () => {
     const [consent, setConsent] = useState(null);
     
     const handleConsent = (accepted) => {
       // Store minimal consent data
       localStorage.setItem('gdpr-consent', JSON.stringify({
         accepted,
         timestamp: new Date().toISOString(),
         version: '1.0'
       }));
       setConsent(accepted);
     };
     
     return (
       <div className="cookie-banner" role="dialog" aria-live="polite">
         <p>نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك</p>
         <button onClick={() => handleConsent(true)}>موافق</button>
         <button onClick={() => handleConsent(false)}>رفض</button>
       </div>
     );
   };
```

2. **Data Subject Request System**

   ```javascript
// api/gdpr/data-request.js
   export default async function handler(req, res) {
     const { email, requestType } = req.body;
     
     switch (requestType) {
       case 'export':
         // Generate user data export
         break;
       case 'delete':
         // Schedule data deletion
         break;
       case 'rectification':
         // Allow data correction
         break;
     }
   }
```

### Day 4-5: Security Hardening

3. **Secrets Rotation Protocol**

   ```bash
# Monthly rotation script
   #!/bin/bash
   NEW_SECRET=$(openssl rand -base64 32)
   vercel env rm PAYLOAD_SECRET
   vercel env add PAYLOAD_SECRET $NEW_SECRET
```

4. **Backup Automation**

   ```javascript
// scripts/backup.js
   const backup = async () => {
     const timestamp = new Date().toISOString().split('T')[0];
     await exec(`mongodump --uri=${MONGODB_URI} --out=./backups/${timestamp}`);
     await uploadToB2(`./backups/${timestamp}`);
   };
   
   // Schedule: Every night at 2 AM Tunis time
   cron.schedule('0 2 * * *', backup);
```

**Security Audit Checklist**:

- [ ] SQL injection prevention (MongoDB injection)
- [ ] XSS protection (Arabic text sanitization)
- [ ] CSRF tokens implemented
- [ ] Rate limiting on API endpoints
- [ ] Admin panel 2FA enabled

---

## 📋 Phase 5: Testing & Quality Assurance (Week 8)

### Day 1-2: Automated Testing

**Owner**: QA Engineer/Developer

1. **Unit Tests**

   ```javascript
// tests/hooks/syncArabicAfterCreate.test.js
   describe('Arabic Auto-Sync', () => {
     test('creates Arabic draft after French event', async () => {
       const frenchEvent = await createEvent({ 
         title: 'Événement Test', 
         locale: 'fr' 
       });
       
       const arabicDraft = await findArabicDraft(frenchEvent.id);
       expect(arabicDraft.title).toContain('[مسودة]');
       expect(arabicDraft.status).toBe('draft');
     });
     
     test('prevents recursion loop', async () => {
       const arabicEvent = await createEvent({ 
         title: 'فعالية تجريبية', 
         locale: 'ar' 
       });
       
       const duplicates = await findDuplicateArabicDrafts(arabicEvent.id);
       expect(duplicates).toHaveLength(0);
     });
   });
```

2. **Integration Tests**

   ```javascript
// tests/api/events.test.js
   describe('Events API', () => {
     test('volunteer can create event', async () => {
       const response = await request(app)
         .post('/api/events')
         .set('Authorization', `Bearer ${volunteerToken}`)
         .send(eventData);
       
       expect(response.status).toBe(201);
       expect(response.body.status).toBe('draft');
     });
   });
```

### Day 3-4: Localization Testing

3. **Language Cascade Testing**

   ```javascript
// tests/localization.test.js
   test('language fallback works correctly', () => {
     const event = {
       title: { fr: 'Événement', en: 'Event' }, // No Arabic
       description: { fr: 'Description', ar: 'وصف', en: 'Description' }
     };
     
     const arabicView = renderEventInLocale(event, 'ar');
     expect(arabicView.title).toBe('Event'); // Falls back to English
     expect(arabicView.description).toBe('وصف'); // Uses Arabic
   });
```

4. **RTL Visual Testing**
   - Screenshot comparisons (Percy.io or similar)
   - Form input alignment
   - Modal dialog positioning
   - Navigation menu behavior

### Day 5: User Acceptance Testing

5. **Volunteer Testing Session**
   - 5 volunteers test core workflows
   - Screen recordings for usability analysis
   - Feedback form in French/Arabic

   **Test Scenarios**:
   - Create and publish an event
   - Upload meeting minutes
   - Switch between languages
   - Use mobile interface

**Quality Gates**:

- [ ] 95% test coverage on critical paths
- [ ] Zero accessibility violations (axe-core)
- [ ] <8s load time on 3G mobile
- [ ] Volunteer approval rating >4/5

---

## 📋 Phase 6: Deployment & Launch (Weeks 9-10)

### Week 9: Staging Deployment

**Day 1-2: Staging Environment**

1. **Deploy to Vercel Staging**

   ```bash
# Deploy staging branch
   git checkout staging
   vercel --env=staging
   vercel alias rotary-cms-staging.vercel.app
```

2. **Data Migration Strategy**

   ```javascript
// scripts/migrate.js
   const migrateExistingData = async () => {
     const existingEvents = await fetchFromOldSystem();
     
     for (const event of existingEvents) {
       await payload.create({
         collection: 'events',
         data: transformLegacyEvent(event),
         locale: detectLanguage(event.title)
       });
     }
   };
```

**Day 3-5: Final Testing**

- Load testing (Artillery.io): 100 concurrent users
- Security penetration testing
- Backup/restore drill validation
- GDPR compliance audit

### Week 10: Production Launch

**Day 1-2: Production Deployment**

1. **Go-Live Checklist**

   ```markdown
## Pre-Launch Checklist
   - [ ] All tests passing
   - [ ] Security scan completed
   - [ ] Backups tested and verified
   - [ ] DNS records updated
   - [ ] SSL certificates valid
   - [ ] Monitoring alerts configured
   - [ ] Rollback plan documented
```

2. **Launch Window**: Sunday 2 AM Tunis Time
   - Minimal user activity
   - Support team on standby
   - Rollback ready within 15 minutes

**Day 3-5: Post-Launch**

3. **Documentation Handover**

   ```
Documentation Package:
   ├── User Guides/
   │   ├── volunteer-guide-fr.pdf
   │   ├── volunteer-guide-ar.pdf
   │   └── admin-manual-en.pdf
   ├── Technical Docs/
   │   ├── deployment-guide.md
   │   ├── troubleshooting.md
   │   └── api-documentation.md
   └── Videos/
       ├── event-creation-tutorial-fr.mp4
       └── arabic-rtl-demo.mp4
```

4. **Training Sessions**
   - 2-hour Zoom session for volunteers (French)
   - 1-hour admin training session
   - Record all sessions for future reference

**Launch Success Criteria**:

- [ ] Zero critical bugs in first 48 hours
- [ ] 100% volunteer activation within 1 week
- [ ] First event published successfully in all languages
- [ ] Mobile usage >60% (analytics validation)

---

## 📋 Phase 7: Post-Launch & Maintenance

### Month 1: Monitoring & Optimization

**Week 1-2: Performance Monitoring**

- Setup Vercel Analytics + Google Analytics
- Monitor Core Web Vitals
- Track language usage patterns
- Measure volunteer engagement

**Week 3-4: Feedback Integration**

- Collect volunteer feedback (Typeform survey)
- Analyze user behavior (Hotjar/LogRocket)
- Prioritize enhancement requests
- Plan first iteration

### Ongoing Maintenance Protocol

**Monthly Tasks**:

- [ ] Security updates (npm audit, dependency updates)
- [ ] Performance review (Core Web Vitals)
- [ ] Backup verification (test restore)
- [ ] Analytics review (usage patterns)

**Quarterly Tasks**:

- [ ] GDPR compliance audit
- [ ] Security penetration test
- [ ] Volunteer satisfaction survey
- [ ] Blueprint update with lessons learned

**Annual Tasks**:

- [ ] Full security audit by external firm
- [ ] Infrastructure cost optimization
- [ ] Technology stack evaluation
- [ ] Strategic roadmap planning

---

## 🎯 Key Success Metrics

### Technical KPIs

| Metric | Target | Measurement |
|--------|--------|-------------|
| Page Load Time | <8s on 3G | WebPageTest |
| Uptime | 99.5% | Vercel Analytics |
| Arabic RTL Accuracy | 100% | Manual QA |
| Mobile Usage | >60% | Google Analytics |

### Business KPIs

| Metric | Target | Measurement |
|--------|--------|-------------|
| Event Publishing Time | 50% reduction | User timing |
| Volunteer Adoption | 100% in 2 weeks | Admin analytics |
| Content in Arabic | 80% coverage | CMS reports |
| User Satisfaction | >4.5/5 | Quarterly survey |

### Compliance KPIs

| Metric | Target | Measurement |
|--------|--------|-------------|
| GDPR Compliance | 100% | External audit |
| Accessibility | WCAG AA | axe-core |
| Security Vulnerabilities | Zero critical | Monthly scans |
| Data Backup Success | 100% | Automated tests |

---

## 🚨 Risk Mitigation Strategies

### Technical Risks

| Risk | Probability | Impact | Mitigation |
|------|-------------|--------|------------|
| Arabic RTL Issues | Medium | High | Extensive testing, RTL expert consultation |
| Auto-sync Recursion | Low | High | Safeguards, monitoring, circuit breakers |
| Mobile Performance | Medium | Medium | Progressive enhancement, lazy loading |
| Third-party Failures | Low | Medium | Fallback systems, vendor SLAs |

### Business Risks

| Risk | Probability | Impact | Mitigation |
|------|-------------|--------|------------|
| Volunteer Resistance | Medium | High | Training, gradual rollout, support |
| Content Migration Issues | Low | Medium | Staged migration, rollback plan |
| GDPR Non-compliance | Low | High | Legal review, compliance testing |
| Budget Overrun | Low | Medium | Fixed-price contracts, scope control |

---

## 📞 Support & Escalation

### Support Tiers

1. **Level 1**: Volunteer self-service (documentation, videos)
2. **Level 2**: Admin assistance (Rotary admin team)
3. **Level 3**: Technical support (development team)

### Contact Matrix

| Issue Type | Contact | Response Time |
|------------|---------|---------------|
| Password reset | <<EMAIL>> | 24 hours |
| Technical bug | <<EMAIL>> | 4 hours |
| Security incident | <<EMAIL>> | 1 hour |
| GDPR request | <<EMAIL>> | 72 hours |

---

*This framework ensures systematic implementation while maintaining focus on the unique trilingual, cultural, and technical requirements of Rotary Club Tunis Doyen.*
cultural, and technical requirements of Rotary Club Tunis Doyen.*
